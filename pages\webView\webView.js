// pages/webView/webView.js
let utilMd5 = require('../../util/md5.js');
let P8SDK = require('../../util/p8sdk-wechat-1.0.51.js')
let P8DECODE = require('../../util/p8sdk-wechat-decode.1.0.0')
let P8SDKConfig = require("../../util/p8sdk-wechat-config.js");
let app = getApp();
let storeId = P8SDKConfig.storeId
let baseUrl = "https://center.yyingplay.com"
let baseH5Url = `${baseUrl}/html/tyh5cbl/index.html#/?`
Page({

  /**
   * 页面的初始数据
   */
  data: {
    gameVersion: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('onLoad--->webView界面', options);
    let that = this
    let params = {}
    let extraData = {}

    if (this.isValidJSON(options.params)) {
      console.warn('首页跳转WebView界面传值', JSON.parse(options.params))
      try {
        params = JSON.parse(options.params)
      } catch (error) {
        params = {}
      }
    } else {
      console.log('false 处理不能parse数据params');
      params = {}
    }
    if (this.isValidJSON(options.extraData)) {
      console.warn('首页跳转WebView界面传值', JSON.parse(options.extraData))
      try {
        extraData = JSON.parse(options.extraData)
      } catch (error) {
        extraData = {}
      }
    } else {
      console.log('false 处理不能parse数据extraData');
      extraData = {}
    }

    const webViewFlag = wx.getStorageSync('webViewFlag')
    const navBgColor = wx.getStorageSync('navBgColor')
    console.log('webViewFlag', webViewFlag);
    console.log('navBgColor', navBgColor);
    if (webViewFlag == 1) {
      P8SDK.login().then((res) => {
        console.error('调用成功', res);
        let d = P8DECODE.P8SDK.init(res, P8SDKConfig.appid);
        console.log('d', d);
        let url = app.globalData.webViewOriginPath + '' + '?result=' + res.result + "&openid=" + d + "&uid=" + res.data.uid + "&sessionid=" + res.data.sessionid + '&gameId=' + storeId + '&appid=' + P8SDKConfig.appid + '&timestampTy=' + Date.now() + '&siteTy=' + res.data.sdkData.site + '&keyTy=' + res.data.sdkData.key + '&aidTy=' + res.data.sdkData.aid + '&platformTy=wx';
        console.log(Object.keys(extraData));
        let addArr = Object.keys(extraData).reduce((pre, item) => {
          if (item != 'key') {
            pre.push(item)
          }
          return pre
        }, [])
        console.log('addArr', addArr);
        if (addArr.length > 0) {
          for (let i = 0; i < addArr.length; i++) {
            url = url += `&${addArr[i]}=` + extraData[addArr[i]]
          }
        }
        console.error('url--->', url);
        app.globalData.webViewPath = url
        app.globalData.webViewOriginPath = url
        app.globalData.webViewAdPath = url
        this.setData({
          path: url
        })
        console.error('webPath--->', this.data.path);
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: navBgColor,
          animation: {
            duration: 400,
            timingFunc: 'easeIn'
          }
        })
        if (res.data.status == '3') {
          let time = parseInt((new Date).getTime() / 1e3);
          let sign = `${params.site}mobile=${params.mobile}&openid=${res.data.openid}&site=${params.site}&time=${time}&uid=${res.data.uid}${params.key}`
          console.log(sign);
          wx.request({
            url: baseUrl + '/api/wxBindMobile',
            data: {
              site: params.site,
              mobile: params.mobile,
              uid: res.data.uid,
              time: time,
              openid: res.data.openid,
              sign: utilMd5.hexMD5(sign)
            },
            method: 'POST',
            success: function (res) {
              console.error('调用微信用户绑定手机成功', res);
              if (res.data.result === 0) {

              } else if (res.data.result == 1 && res.data.data.errorcode == '2107') {
                wx.setStorageSync('phoneType', res.data.data.errorcode)
              } else {

              }
            }
          })
        }
      })
    } else {
      wx.redirectTo({
        url: `/pages/index/index`,
      })
    }
    wx.hideHomeButton({
      complete: () => {}
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {
    console.log('app.globalData.webViewPath', app.globalData.webViewPath);
    let backPath = decodeURI(app.globalData.webViewPath)
    let str = backPath.split('#')
    if (str[1]) {
      console.log('str[1]', str[1]);
      if (this.isValidJSON(str[1])) {
        let JSONS = JSON.parse(str[1])
        console.log('JSONS', JSONS);
        if (JSONS.type === 'rewardVedio') {
          this.setData({
            path: app.globalData.webViewPath
          })
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  onShareAppMessage() {
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: '/pages/homePage/homePage',
        })
      }, 2000)
    })
    return {
      path: '/pages/homePage/homePage',
      promise
    }
  },
  postMessage(e) {
    console.log(e);
  },
  isValidJSON(str) {
    console.log('str', str);
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  },
  gotoAd() {
    let adUitld = 'wwwwwww'
    let time = Date.now() // 时间戳，每次调用广告都更新一次时间戳。
    wx.navigateTo({
      url: `/pages/advertising/advertising?gotoPay_type=h5&adUitld=${adUitld}&time=${time}`
    });
  }
})