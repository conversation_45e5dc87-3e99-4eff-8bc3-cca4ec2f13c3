/* pages/pay/pay.wxss */
.payStaus {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-bottom: 2rpx solid #cccccc;
}

.sumbit-btn {
  margin: 30rpx;
  background-color: #5cb7e6 !important;
  color: #ffffff !important;
}

.backToGame-btn {
  margin: 30rpx;
}

.gameDetail {
  margin: 30rpx 30rpx 0 30rpx;
  background-color: #edfcff;
}

.gameContent {
  padding: 18rpx;
}

.gameMessage {
  color: #333333;
  margin-bottom: 10rpx;
  white-space: nowrap;
}

/* 稳定版本样式 */
.stableVersionBtn {
  position: absolute;
  width: 80rpx;
  padding: 10rpx;
  text-align: center;
  border-radius: 50%;
  background: #fff;
  color: #333;
  box-shadow: 0 0 12rpx rgba(0, 0, 0, .12);
  font-size: 28rpx;
  opacity: 0.8;
}

.stableVersionBarge {
  position: absolute;
  right: -10rpx;
  top: -15rpx;
  background-color: #fc011a;
  border-radius: 50%;
  color: #FFF;
  width: 40rpx;
  text-align: center;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 24rpx;
  opacity: 0.8;
}

.highStableVersionBtn {
  opacity: 1;
}

.movable-area {
  z-index: 100;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  overflow-y: auto;
}

.movable-area::-webkit-scrollbar {
  display: none;
}

.movable-view {
  pointer-events: auto;
  width: 135rpx;
  height: 167rpx;
}

@keyframes wiggle {

  0%,
  65% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  70% {
    -webkit-transform: rotate(6deg);
    transform: rotate(6deg);
  }

  75% {
    -webkit-transform: rotate(-6deg);
    transform: rotate(-6deg);
  }

  80% {
    -webkit-transform: rotate(6deg);
    transform: rotate(6deg);
  }

  85% {
    -webkit-transform: rotate(-6deg);
    transform: rotate(-6deg);
  }

  90% {
    -webkit-transform: rotate(6deg);
    transform: rotate(6deg);
  }

  95% {
    -webkit-transform: rotate(-6deg);
    transform: rotate(-6deg);
  }

  100% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}

.giftLogo {
  animation: wiggle 2s 0s infinite;
  -webkit-animation: wiggle 2s 0s infinite;
  transform-origin: bottom;
  -webkit-transform-origin: bottom;
}

/* pages/downloadPage/downloadPage.wxss */
/* animation: name duration timing-function delay iteration-count direction fill-mode play-state; */
.show_box .show_item:nth-child(1) {
  animation: fadeIn-left 1s;
}

.show_box .show_item:nth-child(2) {
  animation: fadeIn-left 2s;
}

.show_box .show_item:nth-child(3) {
  animation: fadeIn-left 3s;
}

from {
  opacity: 0;
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
}

to {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.show_box {
  top: 5%;
  position: fixed;
  width: 100%;
  z-index: 1111;
  box-sizing: border-box;
  padding: 30px;
  color: #fff;
  font-size: 25rpx;
}

.show_box .show_item {
  margin-bottom: 50rpx;
}

.show_box .show_item:nth-child(1) {
  animation: fadeIn-left 1s;
}

.show_box .show_item:nth-child(2) {
  animation: fadeIn-left 2s;
}

.show_box .show_item:nth-child(3) {
  animation: fadeIn-left 3s;
}

.show_box .show_item:nth-child(4) {
  animation: fadeIn-left 3s;
}

.show_box .show_item:nth-child(5) {
  animation: fadeIn-left 3s;
}

.show_box .show_item:nth-child(6) {
  animation: fadeIn-left 3s;
}

.show_box .show_item:nth-child(7) {
  animation: fadeIn-left 3s;
}

.show_box .show_jiao {
  border: 1px dashed #fff;
  width: 95rpx;
  height: 40rpx;
  margin-left: 20px;
  text-align: center;
  vertical-align: top;
  border-radius: 20rpx;
  font-size: 30px;
}

.show_box .show_jiao view {
  width: 13rpx;
  height: 13rpx;
  background: #fff;
  border-radius: 50%;
  box-shadow: 22rpx 0rpx #fff, -22rpx 0 #fff;
  margin: 0 auto;
}

.show_box .box_index {
  font-size: 20rpx;
  flex-shrink: 0;
  color: #fff;
  line-height: 40rpx;
  width: 40rpx;
  height: 40rpx;
  text-align: center;
  border-radius: 50%;
  background: #e4a451;
  margin-right: 20rpx;
}

.show_box .show_btn {
  border: 1px dashed #fff;
  width: 70%;
  animation: fadeIn 7s;
  font-size: 30rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 44rpx;
  margin: 12% auto 0 auto;
  color: #fff;
}

.flex {
  display: flex;
}

.hover_btn {
  background: #999;
}

.downLoad_banner {
  width: 100%;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bannerImg {
  width: 100%;
  height: 100%;
  background-color: #eeeeee;
  position: fixed;
  background-size: 100% 100%;
}

.downLoadingImg {
  width: 325rpx;
  height: 96rpx;
  position: absolute;
  bottom: 25%;
  left: 0;
  right: 0;
  margin: auto;
}

.closeImg {
  width: 216rpx;
  height: 66rpx;
  position: absolute;
  bottom: 15%;
  left: 50%;
  right: 0;
  margin: auto;
}

.noShowImg {
  width: 216rpx;
  height: 66rpx;
  position: absolute;
  bottom: 15%;
  left: 0;
  right: 50%;
  margin: auto;
}

.downloaQRCodeImg {
  width: 516rpx;
  height: 588rpx;
  position: fixed;
  bottom: 35%;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1111;
}

.phoneAuthBtn {
  width: 80rpx;
  height: 50rpx;
  line-height: 50rpx;
  padding: 0;
  font-size: 24rpx;
  margin: 0 0 0 5rpx;
}

/* 动画 */


@-webkit-keyframes fadeIn-left {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeIn-left {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 遮罩 */
.mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .9);
  -webkit-transition-duration: .3s;
  transition-duration: .3s;
}

.pay_box {
  position: fixed;
  z-index: 1001;
  width: 677rpx;
  height: 621rpx;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.phoneBtn::after {
  border: none;
}

.phoneBtn[disabled][type="default"],
wx-button[disabled]:not([type]) {
  background-color: transparent;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.originalCost {
  text-decoration: line-through;
  color: #666666;
}

.nicePrice {
  color: #fc011a;
}

.fsNormal {
  font-size: 28rpx;
}

.privacy {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, .6);
  z-index: 9999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  width: 632rpx;
  padding: 48rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  height: 450rpx;
}

.content .title {
  text-align: center;
  color: #333;
  font-weight: bold;
  font-size: 32rpx;
}

.content .des {
  font-size: 26rpx;
  color: #666;
  margin-top: 40rpx;
  text-align: justify;
  line-height: 1.6;
}

.content .des .link {
  color: #07c160;
  text-decoration: underline;
}

.btns {
  margin-top: 100rpx;
  display: flex;
}

.btns .item {
  justify-content: space-between;
  width: 244rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  box-sizing: border-box;
  border: none;
}

.btns .reject {
  background: #f4f4f5;
  color: #909399;
}

.btns .agree {
  background: #07c160;
  color: #fff;
}