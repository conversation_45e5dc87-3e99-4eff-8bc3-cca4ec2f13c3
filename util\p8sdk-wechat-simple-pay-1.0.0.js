let utilMd5 = require('../util/md5');
var P8SIMPLESDK = {}
let sdkData = {
  platform_url: "",
  data_url: "",
  key_android: "",
  site_android: "",
  key_ios: "",
  site_ios: "",
  aid: "",
  monitorAid: "",
  uid: "",
  sid: "",
  sname: "",
  roleid: "1",
  rolename: "1",
  vip: "",
  level: "",
  gold: "",
  mac: "",
  device: "",
  modeltype: "wx",
  device_type: "1",
  gameversion: "",
  device_model: "",
  device_resolution: "",
  device_version: "",
  device_net: "",
  platform: "",
  initOnshowFlag: true,
  onshowFlag: true,
  account: "",
  password: "",
};
var baseUrl = 'https://recharge.yyingplay.com'
P8SIMPLESDK.initData = function (i) {
  miniPro_ChangeUndefined(i);
  Object.assign(sdkData, i);
  wx.getSystemInfo({
    success: function (res) {
      if (res.platform == "ios") {
        sdkData.aid = sdkData.aid_ios;
        sdkData.key = sdkData.key_ios;
        sdkData.site = sdkData.site_ios;
      } else {
        sdkData.aid = sdkData.aid_android;
        sdkData.key = sdkData.key_android;
        sdkData.site = sdkData.site_android;
      }
    }
  });
}
P8SIMPLESDK.pay = function (g) {
  let e = new Promise((l, e) => {
    let c = parseInt(new Date().getTime() / 1e3);
    var oReqData = {
      cp_order_id: g.cp_order_id,
      money: g.money,
      product_name: g.product_name,
      productid: g.productid,
      roleid: g.roleid,
      rolename: g.rolename,
      serverid: g.serverid,
      ext: g.ext,
    };
    var i = "";
    i = miniPro_newSignGetType(oReqData);
    var n = utilMd5.hexMD5(`${c}${sdkData.site}${i}${sdkData.key}${c}`);
    oReqData.sign = n;
    miniPro_madeOrder(oReqData).then((d) => {
      console.log(d);
      if (d.data.result != 0) {
        l({
          result: -1,
          msg: "下单异常:",
          info: JSON.stringify(d.data)
        });
      }
      let result = d.data;
      if (!result) {
        l({
          result: -1,
          msg: "获取格式错误",
          info: "获取格式错误",
        });
      }
      console.info(" 下单返回内容", JSON.stringify(result.data));
      l(result.data)
    })
  })
  return e;
}

function miniPro_madeOrder(params) {
  let result = new Promise((l, e) => {
    let newSite = sdkData.b_site ? sdkData.b_site : sdkData.site;
    let time = parseInt(new Date().getTime() / 1e3);
    var mdData = {
      cp_order_id: params.cp_order_id,
      money: params.money,
      product_name: params.product_name,
      productid: params.productid,
      roleid: params.roleid,
      rolename: params.rolename,
      serverid: params.serverid,
      vip: params.vip,
      level: params.level,
      ext: params.ext,
      ce: params.ce,

      aid: sdkData.aid,
      device_type: sdkData.device_type,
      udid: sdkData.device,
      uid: sdkData.uid,
      time: time,
      site: newSite,
      ip: "",
      json: 1,
    };

    let noudf = miniPro_newSignGetType(mdData);
    let sign = utilMd5.hexMD5(`${time}${sdkData.site}${noudf}${sdkData.key}${time}`);
    mdData.sign = sign;
    console.log('mdData', mdData);
    // let url = 'https://recharge.play800.cn/h/p';
    let url = baseUrl + '/h/p'
    wx.request({
      url: url,
      data: mdData,
      method: "GET",
      success: function (res) {
        if (res.data.result == 0) {
          l(res)
        }
      }
    })
  });
  return result
}

function miniPro_newSignGetType(t) {
  miniPro_ChangeUndefined(t);
  var a = [];
  for (var e in t) {
    a.push(e);
  }
  a = a.sort();
  let i = "";
  for (let e = 0; e < a.length; e++) {
    const n = t[a[e]];
    if (e != 0) {
      i += a[e] + n;
    } else {
      i += a[e] + n;
    }
  }
  return i;
}

function miniPro_ChangeUndefined(t) {
  for (let e in t) {
    if (t.hasOwnProperty(e)) {
      if (typeof t[e] == "undefined") {
        t[e] = "";
      }
    }
  }
}

module.exports = P8SIMPLESDK