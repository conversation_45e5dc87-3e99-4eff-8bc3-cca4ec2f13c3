let utilMd5 = require('./md5');
var wx_videoAD;
var appid;
var openid;
var gotoPay_order;
var order_list = [];
var site;
// var baseUrl = 'https://trecharge.play800.cn'
// var baseUrl = 'https://recharge.play800.cn'
var baseUrl = "https://recharge.yyingplay.com"
var P8SDK = {};

/* 激励视频初始化 */
P8SDK.wxADinit = function (e, t, o, n) {
  if (e) {
    console.log("激励视频id ", e);
    wx_videoAD = wx.createRewardedVideoAd({
      adUnitId: e,
      multiton: true, // 多个广告单例 多次创建广告id会返回不同的实例（不同的激励广告id数据会正常） 会造成内存增加
    });
    wx_videoAD.onLoad(() => {
      console.log("激励视频 广告加载成功");
    });
    wx_videoAD.onError((e) => {
      console.log("激励视频 广告加载异常", e);
    });
  }
}

/* 激励视频激活显示 */
P8SDK.videoADShow = function (that) {
  if (!wx_videoAD || !wx_videoAD.show) {
    console.log("激励视频不存在");
    return;
  }
  wx_videoAD.show().catch((e) => {
    console.log("激励视频 广告加载异常 再次广告加载", e);
    wx_videoAD.load().then(() => wx_videoAD.show());
  });
  const i = (e) => {
    wx_videoAD.offClose(i);
    if ((e && e.isEnded) || e === undefined) {
      console.log("正常播放结束，可以下发游戏奖励");
      that.isADsureOk()
    } else {
      console.log("播放中途退出，不下发游戏奖励");
      that.isADNotOk()
    }
  };
  wx_videoAD.onClose(i);
};

/* 发货初始化 */
P8SDK.wxShipInit = function (id, order, s, appids, type) {
  console.log("openid--->", id);
  console.log("gotoPay_order订单号--->", order);
  openid = id
  gotoPay_order = order
  appid = appids
  site = s
  console.log('id--->', id, 'order--->', order, 'site--->', site, 'appid--->', appids);
  wxShipCheckList(type)
};

/* 查询发货订单列表 */
function wxShipCheckList(type) {
  console.log('wxShipCheckList--->', openid);
  console.log('wxShipCheckList--->', gotoPay_order);
  console.log('wxShipCheckList--->', appid);
  let time = parseInt((new Date).getTime() / 1e3);
  let sign = `${site}appid=${appid}&openid=${openid}&site=${site}&time=${time}`
  console.log('wxShipCheckList签名', sign);
  let params = {
    site: site,
    appid: appid,
    time: time,
    sign: utilMd5.hexMD5(sign)
  }
  if (openid) {
    params.openid = openid
  }
  wx.request({
    url: `${baseUrl}/api/getOrderLists`,
    method: "GET",
    data: params,
    success: function (res) {
      console.log('查询发货订单列表---->', res);
      if (res.data.result == 0) {
        order_list = res.data.data.data.order_list
        for (let item of order_list) {
          if (item.openid == openid && item.merchant_trade_no == gotoPay_order) {
            console.log('order_list', item);
            wxShipCheckStatus(item)
          }
        }
        if (type == 'all') {
          for (let item of order_list) {
            if (item.order_state == 1) {
              wxShipSendMent(item)
            }
            // wxShipSendMent(item)
            // wxShipCheckStatus(item)
          }
        }
      } else {
        wx.showToast({
          title: res.data.errmsg,
          icon: 'none',
          duration: 1500,
          mask: true
        })
      }
    }
  })
};

/* 查询订单发货状态 */
function wxShipCheckStatus(row) {
  console.log('wxShipCheckStatus', row);
  let time = parseInt((new Date).getTime() / 1e3);
  let sign = `${site}appid=${appid}&merchant_id=${row.merchant_id}&merchant_trade_no=${row.merchant_trade_no}&site=${site}&time=${time}&transaction_id=${row.transaction_id}`
  console.log('wxShipCheckStatus签名', sign);
  wx.request({
    url: `${baseUrl}/api/getOrderStatus`,
    method: "GET",
    data: {
      site: site,
      appid: appid,
      time: time,
      sign: utilMd5.hexMD5(sign),
      transaction_id: row.transaction_id,
      merchant_id: row.merchant_id,
      merchant_trade_no: row.merchant_trade_no,
    },
    success: function (res) {
      console.log('查询订单发货状态---->', res);
      if (res.data.result == 0) {
        if (res.data.data.data.order_info.order_state == 1) {
          wxShipSendMent(res.data.data.data.order_info)
        }
      } else {
        wx.showToast({
          title: res.data.errmsg,
          icon: 'none',
          duration: 1500,
          mask: true
        })
      }
    }
  })
};

/* 发货录入接口 */
function wxShipSendMent(row) {
  console.log('wxShipSendMent', row);
  let time = parseInt((new Date).getTime() / 1e3);
  let sign = `${site}appid=${appid}&item_desc=商城商品&mchid=${row.merchant_id}&openid=${openid}&order_number_type=1&out_trade_no=${row.out_trade_no}&site=${site}&time=${time}&transaction_id=${row.transaction_id}`
  console.log('wxshipSendMent签名', sign);
  wx.request({
    url: `${baseUrl}/api/orderShipment`,
    method: "POST",
    data: {
      site: site,
      appid: appid,
      time: time,
      openid: openid,
      item_desc: '商城商品',
      order_number_type: '1',
      mchid: row.merchant_id,
      out_trade_no: row.merchant_trade_no,
      transaction_id: row.transaction_id,
      sign: utilMd5.hexMD5(sign),
    },
    success: function (res) {
      console.log('发货录入接口---->', res);
      if (res.data.result == 0) {

      } else {
        wx.showToast({
          title: res.data.errmsg,
          icon: 'none',
          duration: 1500,
          mask: true
        })
      }
    }
  })
};
module.exports = P8SDK