// index.js
let P8SDK = require('../../util/p8sdk-wechat-mini-ad.js')
let P8SDKLogin = require('../../util/p8sdk-wechat-1.0.51.js')
let P8SDKConfig = require("../../util/p8sdk-wechat-config.js");
let P8SWITCHSDK = require('../../util/p8sdk-wechat-switch-2.0.0-ty');
let utilMd5 = require('../../util/md5.js');
let appid = P8SDKConfig.appid
wx.setStorageSync('appid', appid)
let phoneType = wx.getStorageSync('phoneType')
let rewardsBarge = wx.getStorageSync('rewardsBarge')
Page({
  data: {
    baseUrl: "https://center.yyingplay.com",
    baseSite: "",
    baseKey: "",
    baseOpenid: "",
    phoneBtnType: false,
    phoneBtnType2: false,
    rewardsBarge: 0,
    advertisement_id: '',
    btnImageWidth: 0,
    btnImageHeight: 0,
    logoImageWidth: 0,
    logoImageHeight: 0,
    s_p: '',
    bg: '',
    byPass: false,
    scenePass: false,
    stepFlag: 1,
    loadingBar: 0,
    timer: null,
    margintop: '',
    funcrowheight: '',
    adTime: 1,
    skipAdStatus: false,
    shopShow: 1,
    sceneValue: -1,
    ttshowFlag: -1,
  },
  onLoad(option) {
    let _this = this
    wx.showLoading({
      title: '加载中...',
    })
    _this.setData({
      timer: setInterval(_this.countTime, 1000)
    })
    wx.getSystemInfo({
      success: function (res) {
        console.log('获取到的设备信息', res)
        console.warn('config配置项信息', P8SDKConfig);
        wx.setStorageSync('platform', res.platform)
        if (res.platform == "ios") {
          _this.setData({
            baseSite: P8SDKConfig.site_ios,
            baseKey: P8SDKConfig.key_ios,
            rewardsBarge: rewardsBarge ? rewardsBarge : 0,
          })
        } else {
          _this.setData({
            baseSite: P8SDKConfig.site_android,
            baseKey: P8SDKConfig.key_android,
            rewardsBarge: rewardsBarge ? rewardsBarge : 0,
          })
        }
      }
    });
    wx.request({
      url: _this.data.baseUrl + '/sdk_callback/setLog',
      data: {
        log: 'u_ip',
        platform: 'wx',
        appid: appid,
      },
      method: 'GET',
      success: function (res) {
        console.error('setLog', res.data);
      }
    })
    wx.getImageInfo({
      src: '../../assets/image/button.png',
      success(res) {
        _this.setData({
          btnImageWidth: (res.width * 2) + 'rpx',
          btnImageHeight: (res.height * 2) + 'rpx'
        })
      }
    })
    const {
      height,
      top
    } = wx.getMenuButtonBoundingClientRect();
    // 定义功能菜单行与胶囊对其
    _this.setData({
      margintop: top,
      funcrowheight: height
    })
    _this.handleFahuo()
  },
  onShow(option) {
    let _this = this
    let referrerInfo = wx.getEnterOptionsSync()
    console.log('场景值：', getApp().globalData.scene)
    let scene = _this.handleScene(getApp().globalData.scene)
    console.warn('scenePass状态', scene);
    _this.setData({
      scenePass: scene,
      sceneValue: getApp().globalData.scene,
    })
    let time = parseInt((new Date).getTime() / 1e3);
    /* ext=appid */
    let extsign = `${_this.data.baseSite}default=0&ext=appid&site=${_this.data.baseSite}&time=${time}&type=42${_this.data.baseKey}`
    wx.request({
      url: _this.data.baseUrl + '/switch/switchList',
      data: {
        site: _this.data.baseSite,
        type: '42',
        default: '0',
        time,
        ext: 'appid',
        sign: utilMd5.hexMD5(extsign)
      },
      method: "GET",
      success: function (result) {
        if (result.data.result == 0) {
          wx.setStorageSync('extAppid', result.data.data.appid)
        }
      }
    })
    /* 二维码 */
    let ertime = parseInt((new Date).getTime() / 1e3);
    let ersign = `${_this.data.baseSite}default=0&ext=money|url&site=${_this.data.baseSite}&time=${ertime}&type=45${_this.data.baseKey}`
    wx.request({
      url: _this.data.baseUrl + '/switch/switchList',
      data: {
        site: _this.data.baseSite,
        type: '45',
        default: '0',
        time: ertime,
        ext: 'money|url',
        sign: utilMd5.hexMD5(ersign)
      },
      method: "GET",
      success: function (result) {
        if (result.data.result == 0) {
          wx.setStorageSync('bgUrl', result.data.data.url)
          wx.setStorageSync('moneyPay', result.data.data.money)
        }
      }
    })
    /* 半屏ad */
    let adtime = parseInt((new Date).getTime() / 1e3);
    let adsign = `${_this.data.baseSite}default=0&ext=appid|adUitld&site=${_this.data.baseSite}&time=${adtime}&type=48${_this.data.baseKey}`
    wx.request({
      url: _this.data.baseUrl + '/switch/switchList',
      data: {
        site: _this.data.baseSite,
        type: '48',
        default: '0',
        time: adtime,
        ext: 'appid|adUitld',
        sign: utilMd5.hexMD5(adsign)
      },
      method: "GET",
      success: function (result) {
        if (result.data.result == 0) {
          console.error('半屏广告开关', result.data.data);
          wx.setStorageSync('bpappid', result.data.data.appid)
          wx.setStorageSync('adUitld', result.data.data.adUitld)
        }
      }
    })
    _this.getGongzhonghaoQrCode();
    _this.getTTGuosheng();
    // _this.getNavBarColor()
    P8SDKLogin.login().then((res) => {
      console.error('P8SDKLogin', res);
      if (res.data.status == 0) {
        _this.setData({
          phoneBtnType2: false,
        })
      } else if (phoneType == '2107') {
        _this.setData({
          phoneBtnType2: false,
        })
      } else if (_this.data.webViewFlag == '2') {
        _this.setData({
          phoneBtnType2: false,
        })
      } else {
        _this.setData({
          phoneBtnType2: true,
        })
      }
    })
  },
  gotoWebView(params) {
    console.log('status', this.data.webViewFlag == '1' ? '开启' : '关闭');
    if (params.type == 'tap') {
      params = ''
    }
    if (this.data.webViewFlag === 1) {
      let referrerInfo = wx.getEnterOptionsSync()
      let extraData = ''
      if (referrerInfo.referrerInfo.extraData) {
        extraData = JSON.stringify(referrerInfo.referrerInfo.extraData)
      }
      console.log('extraData', extraData);
      extraData ? extraData : ''
      wx.redirectTo({
        url: `/pages/webView/webView?params=${params}&extraData=${extraData}`,
      })
    } else {
      wx.navigateTo({
        url: '/pages/store/store',
      })
    }
  },
  // 广告按钮
  guanggaoShow() {
    // 在页面中定义激励视频广告
    // 在页面onLoad回调事件中创建激励视频广告实例
    let that = this
    wx.showToast({
      title: '未开启',
      icon: 'none',
    })
    console.log(that.data.advertisement_id);
    P8SDK.wxADinit(that.data.advertisement_id)
    P8SDK.videoADShow(that)

  },
  // 看完激励视频奖励
  isADsureOk() {
    let that = this
    this.setData({
      rewardsBarge: ++that.data.rewardsBarge
    })
    wx.showToast({
      title: '获得9.5折卡',
    })
    wx.setStorageSync('rewardsBarge', that.data.rewardsBarge)
  },
  // 未看完激励视频奖励
  isADNotOk() {
    console.log('no OK');
  },
  // 点击按钮授权是否获取手机号
  getPhoneNumber(e) {
    var that = this;
    var msg = e.detail.errMsg;
    console.error('点击拉取手机号返回', msg);
    var encryptedData = e.detail.encryptedData;
    var iv = e.detail.iv;
    var code = e.detail.code;
    if (msg == 'getPhoneNumber:ok') { //这里表示获取授权成功
      wx.checkSession({
        success: function () {
          //这里进行请求服务端解密手机号
          that.getPhoneRequest(code)
        },
        fail: function () {
          // that.userlogin()
        }
      })
    } else {
      if (!(this.data.s_p)) {
        let params = {
          type: 'tap'
        }
        that.gotoWebView(params)
      }
    }
  },
  // 获取手机号接口
  getPhoneRequest(code) {
    let that = this
    let time = parseInt((new Date).getTime() / 1e3);
    let sign = `${that.data.baseSite}appid=${appid}&js_code=${code}&site=${that.data.baseSite}&time=${time}${that.data.baseKey}`
    wx.request({
      url: that.data.baseUrl + '/oauth/getPhoneNumber',
      data: {
        site: that.data.baseSite,
        appid: appid,
        js_code: code,
        time: time,
        sign: utilMd5.hexMD5(sign)
      },
      method: 'GET',
      success: function (res) {
        console.log('调用微信手机号', res);
        if (res.data.result === 0) {
          that.wxLoginToReportFuntions(res)
        } else {
          wx.showToast({
            title: res.data.data.msg,
            icon: 'none'
          })
        }
      }
    })
  },
  wxLoginToReportFuntions(res) {
    let that = this
    let mobile = res.data.data.phoneNumber
    let time = parseInt(new Date().getTime() / 1e3);
    let a = wx.getStorageSync('wxLoginToReportConfig')
    let params = JSON.parse(a)
    params.mobile = mobile
    params.time = time
    params.sign = utilMd5.hexMD5(`${that.data.baseKey}WX${that.data.baseSite}WX${time}${time}`);
    wx.login({
      success(res) {
        if (res.code) {
          params.js_code = res.code
          wx.request({
            url: that.data.baseUrl + '/oauth/wxLoginToReport',
            data: params,
            method: "POST",
            success: function (res) {
              if (res.data.result === 0) {
                let webParams = {
                  site: that.data.baseSite,
                  mobile: mobile,
                  key: that.data.baseKey
                }
                that.gotoWebView(JSON.stringify(webParams))
              } else {
                wx.showToast({
                  title: res.data.data.msg,
                  icon: 'none'
                })
              }
            }
          })
        }
      }
    })
  },
  onShareAppMessage() {
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: '/pages/homePage/homePage',
        })
      }, 2000)
    })
    return {
      path: '/pages/homePage/homePage',
      promise
    }
  },
  onShareTimeline(res) {
    console.log("分享朋友圈")
    console.log(res)
  },
  skipAdBindTap() {
    let that = this
    that.setData({
      skipAdStatus: true,
    })
    let referrerInfo = wx.getEnterOptionsSync()
    that.checkByPassStatus(referrerInfo)
  },
  checkByPassStatus(referrerInfo) {
    console.log('referrerInfo', referrerInfo);
    let _this = this
    let extraData = ''
    let openData = {}
    let timeBindWx = parseInt((new Date).getTime() / 1e3);
    const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
    if (wxLoginToReportResult) {
      openData = JSON.parse(wxLoginToReportResult)
    }
    console.log('openData', openData);
    if (openData.uid) {
      if (referrerInfo.referrerInfo.appId && _this.data.sceneValue == '1037') {
        extraData = JSON.stringify(referrerInfo.referrerInfo.extraData)
        console.error('AG跳转进来------->准备上报');
        let signBindWx = `${_this.data.baseSite}appid=${appid}&site=${_this.data.baseSite}&time=${timeBindWx}&type=bind&uid=${openData.uid}${_this.data.baseKey}`
        wx.request({
          url: _this.data.baseUrl + "/oauth/isBindWx",
          data: {
            site: _this.data.baseSite,
            appid,
            uid: openData.uid,
            type: 'bind',
            time: timeBindWx,
            sign: utilMd5.hexMD5(signBindWx)
          },
          method: "GET",
          success: function (res) {
            if (res.data.result == 0) {
              _this.setData({
                byPass: true
              })
              console.error('上报成功');
            } else {
              console.error('上报失败');
            }
            console.info('日志isBindWx', res.data);
          },
          fail: function (err) {
            console.error('接口调用上报失败--------->fail', err);
          }
        })
      } else {
        console.error('小程序直接进入------->准备查询状态');
        let signBindWx = `${_this.data.baseSite}appid=${appid}&site=${_this.data.baseSite}&time=${timeBindWx}&type=is_bind&uid=${openData.uid}${_this.data.baseKey}`
        wx.request({
          url: _this.data.baseUrl + "/oauth/isBindWx",
          data: {
            site: _this.data.baseSite,
            appid,
            uid: openData.uid,
            type: 'is_bind',
            time: timeBindWx,
            sign: utilMd5.hexMD5(signBindWx)
          },
          method: "GET",
          success: function (res) {
            if (res.data.result == 0) {
              _this.setData({
                byPass: true
              })
              console.error('byPass状态为开');
            } else {
              _this.setData({
                byPass: false
              })
              console.error('byPass状态为关');
            }
            console.info('日志isBindWx', res.data);
          },
          fail: function (err) {
            console.error('接口调用上报失败--------->fail', err);
          }
        })
      }
      console.log('extraData', extraData);
      let time = parseInt((new Date).getTime() / 1e3);
      console.log('获取到的site和key', `site:${_this.data.baseSite}`, `key:${_this.data.baseKey}`)
      wx.request({
        url: _this.data.baseUrl + "/switch/wxShopOver",
        data: {
          site: _this.data.baseSite,
        },
        method: "GET",
        success: function (res) {
          console.error('调用开关接口成功', res)
          if (res.data.result === 0) {
            wx.login({
              success(login_res) {
                if (login_res.code) {
                  let sign = `${_this.data.baseSite}default=1&site=${_this.data.baseSite}&time=${time}&type=54&wx_scene_id=${_this.data.sceneValue}${_this.data.baseKey}`
                  let switchListParmas = {
                    site: _this.data.baseSite,
                    type: '54',
                    default: '1',
                    time,
                    wx_scene_id: _this.data.sceneValue,
                    sign: utilMd5.hexMD5(sign)
                  }
                  wx.request({
                    url: _this.data.baseUrl + '/switch/switchListN',
                    // url: 'https://tcenter.play800.cn/switch/switchListN',
                    data: {
                      site: _this.data.baseSite,
                      type: '54',
                      default: '1',
                      time,
                      wx_scene_id: _this.data.sceneValue,
                      sign: utilMd5.hexMD5(sign)
                    },
                    method: "GET",
                    success: function (result) {
                      console.error('汇总开关黑名单ip参数54', switchListParmas);
                      console.error('汇总开关黑名单ip54', result.data);
                      let rData = {
                        uid: openData.uid
                      }
                      _this.checkCanGotoWebview(rData, res, result, extraData);
                    }
                  })
                }
              }
            })
          }
        }
      })
    } else {
      setTimeout(() => {
        _this.checkByPassStatus(referrerInfo)
      }, 500)
    }
  },
  checkCanGotoWebview(rData, res, result, extraData) {
    let that = this
    P8SWITCHSDK.gotoTtSwitch(rData).then(resolve => {
      P8SDKLogin.login().then((p8result) => {
        console.error('--------P8SDKLogin', p8result);
        console.error('--------resolve', resolve);
        if (res.data.data.s) {
          if (that.data.ttshowFlag == 0) {
            that.print('头条过审开关："打开"')
            if (resolve.result == 0) {
              console.log('if');
              that.loginWebView(res, extraData)
            } else {
              console.log('else');
              if (p8result.data.is_adv) {
                that.loginWebView(res, extraData)
              } else {
                that.blockWebView(res)
              }
            }
          } else {
            that.print('头条过审开关："关闭"')
            that.loginWebView(res, extraData)
          }
        } else {
          that.blockWebView(res)
        }
        let toolsTips = {
          '开关': res.data.data.s,
          '创角黑名单66': JSON.stringify(resolve),
          '头条审核开关': that.data.ttshowFlag,
          '投放状态': p8result.data.is_adv,
          '54黑名单(弃用)': result.data.result,
          '登录信息': JSON.stringify(p8result),
        }
        console.log('所有开关判断', toolsTips)
        console.log('所有开关判断', JSON.stringify(toolsTips))
        wx.setStorageSync('webViewFlag', that.data.webViewFlag)
      })

    })

    wx.hideLoading();
  },
  handleFahuo() {
    let _this = this
    setTimeout(() => {
      const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
      if (wxLoginToReportResult) {
        let openData = JSON.parse(wxLoginToReportResult)
        _this.setData({
          baseOpenid: openData.openid
        })
        P8SDK.wxShipInit(_this.data.baseOpenid, _this.data.gotoPay_order, _this.data.baseSite, appid, 'all')
      }

    }, 1000)
  },
  handleScene(key) {
    if (key == '1011' || key == '1012' || key == '1013' || key == '1025' || key == '1031' || key == '1032' || key == '1047' || key == '1048' || key == '1049' || key == '1124' || key == '1125' || key == '1126' || key == '1000') {
      return false
    } else {
      return true
    }
  },
  loginWebView(res, extraData) {
    console.log('loginWebView{}');
    let that = this
    that.setData({
      webViewFlag: 1,
      phoneBtnType: true,
      advertisement_id: res.data.data.advertisement_id,
      bg: res.data.data.bg,
      s_p: res.data.data.s_p,
      shopShow: 1,
    })
    let arr = that.data.advertisement_id.split("#")
    wx.setStorageSync('discount', arr[0])
    wx.setStorageSync('advertisement_id', arr[1])
    wx.hideLoading()
    let params = ''
    extraData ? extraData : ''
    wx.redirectTo({
      url: `/pages/webView/webView?params=${params}&extraData=${extraData}`,
    })
  },
  blockWebView(res) {
    console.log('blockWebView{}');
    let that = this
    that.setData({
      webViewFlag: 2,
      phoneBtnType: false,
      advertisement_id: res.data.data.advertisement_id,
      bg: res.data.data.bg_sh,
      s_p: res.data.data.s_p,
      shopShow: 2,
    })
    let arr = that.data.advertisement_id.split("#")
    wx.setStorageSync('discount', arr[0])
    wx.setStorageSync('advertisement_id', arr[1])
    wx.hideLoading()
    wx.redirectTo({
      url: '/pages/index/index',
    })
  },
  countTime() {
    let that = this
    let referrerInfo = wx.getEnterOptionsSync()
    var time = that.data.adTime
    if (time == 1 && !(that.data.skipAdStatus)) {
      clearInterval(that.data.timer);
      that.checkByPassStatus(referrerInfo)
    } else {
      that.setData({
        adTime: time - 1
      })
    }
  },
  getGongzhonghaoQrCode() {
    let that = this
    let i = `${that.data.baseUrl}/switch/switchList`;
    let t = parseInt((new Date).getTime() / 1e3);
    let d = {
      site: that.data.baseSite,
      type: '50',
      default: '0',
      time: t,
      ext: "url",
    }
    let a = that.SignGetForCenter(d, that.data.baseKey)
    d.sign = utilMd5.hexMD5(a);
    let n = (t) => {
      // console.error("rrr1-------------", t);
      t = that.dateOrRes(t);
      console.error("50", JSON.stringify(t));
      if (t.result === 0) {
        wx.setStorageSync('GongzhonhaoQRUrl', t.data.url)
      } else {
        console.error("加载getGongzhonghaoQrCode异常", t);
      }
    };
    that.wxRequest(i, "GET", d, n);
  },
  getTTGuosheng() {
    let that = this
    let i = `${that.data.baseUrl}/switch/switchListN`;
    let t = parseInt((new Date).getTime() / 1e3);
    let d = {
      site: that.data.baseSite,
      type: '65',
      default: '0',
      time: t,
    }
    let a = that.SignGetForCenter(d, that.data.baseKey)
    d.sign = utilMd5.hexMD5(a);
    console.log(" '65开关参数 :'", d);
    let n = (t) => {
      // console.error("rrr1-------------", t);
      t = that.dateOrRes(t);
      console.log(" '65开关返回数据 :'", JSON.stringify(t));
      that.setData({
        ttshowFlag: t.result,
      })
    };
    that.wxRequest(i, "GET", d, n);
  },
  getNavBarColor() {
    let that = this
    let i = `${that.data.baseUrl}/switch/switchList`;
    // let i = `https://tcenter.play800.cn/switch/switchList`;
    let t = parseInt((new Date).getTime() / 1e3);
    let d = {
      site: that.data.baseSite,
      type: '56',
      default: '0',
      time: t,
      ext: "nav_bg_color",
    }
    let a = that.SignGetForCenter(d, that.data.baseKey)
    d.sign = utilMd5.hexMD5(a);
    let n = (t) => {
      // console.error("rrr1-------------", t);
      t = that.dateOrRes(t);
      console.error("56", JSON.stringify(t));
      if (t.result === 0) {
        wx.setStorageSync('navBgColor', t.data.nav_bg_color)
      } else {
        console.error("加载navBgColor异常", JSON.stringify(t));
      }
    };
    that.wxRequest(i, "GET", d, n);
  },
  wxRequest(e, t, a, i, n) {
    wx.request({
      url: e,
      method: t,
      data: a,
      success: (e) => {
        if (i) {
          i(e);
        }
      },
      fail: (e) => {
        if (n) {
          n(e);
        }
      },
    });
  },
  SignGetForCenter(e, key) {
    let that = this
    let t = key;
    that.print("新的加密方式请求数据sdkData.key", t);
    var a = [];
    for (var i in e) {
      a.push(i);
    }
    a = a.sort();
    var n = "";
    for (var o = 0; o < a.length; o++) {
      var r = e[a[o]];
      if (o != a.length - 1) {
        n += a[o] + "=" + r + "&";
      } else {
        n += a[o] + "=" + r;
      }
    }
    return e.site + n + t;
  },
  dateOrRes(e) {
    return e.data ? e.data : e;
  },
  print() {
    let t = "";
    for (let e = 0; e < 20; e++) {
      if (!arguments[e]) {
        console.info(t);
        return;
      }
      t += " " + JSON.stringify(arguments[e]);
    }
  }
})