// pages/pay/pay.js
let P8SDK = require('../../util/p8sdk-wechat-mini-ad.js')
let utilMd5 = require('../../util/md5.js');
let P8SDKConfig = require("../../util/p8sdk-wechat-config.js");
let appid = P8SDKConfig.appid
Page({

  /**
   * 页面的初始数据
   */
  data: {
    gotoPay_payStatus: 0,
    gotoPay_goods: '',
    gotoPay_price: '',
    gotoPay_order: '',
    gotoPay_character: '',
    gotoPay_area: '',
    gotoPay_site: '',
    openid: '',
    session_key: '',
    gotoPay_payData: {},
    loginCode: '',
    appid: '',
    baseUrl: 'https://center.yyingplay.cn',
    /* https://center.hntengy.cn */
    /* https://tcenter.play800.cn */
    basePayUrl: 'https://recharge.yyingplay.cn',
    /* 正式服 https://recharge.hntengy.cn */
    /* 测试服 https://trecharge.play800.cn */
    sceneUrl: 'https://recharge.yyingplay.cn',
    /* https://trecharge.play800.cn */
    scene: '',
    webViewFlag: '',
    urlCode: '', // url  zhuanduan
    newTips: false,
    phoneTips: false,
    phoneTipsToPay: false,
    phoneAutoBtn: false,
    p8site: '',
    p8key: '',
    platform: '',
    paySkipNum: 0,
    payMark: false,
    vanOverlay: false,
    showTips: false,
    bgcode: '',
    moneyPay: '30',
    userStatus: '-1',
    GongzhonhaoQRUrl: "",
    GongzhonhaoCodePayMark: false,
   
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.hideHomeButton({
      complete: () => {}
    })
    console.warn('onLoad加载')
    console.warn(options);
    let _this = this
    let obj = wx.getLaunchOptionsSync()
    let data = ''
    console.error('scene', options.scene);
    const webViewFlag = wx.getStorageSync('webViewFlag')
    const scene = decodeURIComponent(options.scene)
    const payMark = wx.getStorageSync('payMark')
    const bgcode = wx.getStorageSync('bgUrl')
    const moneyPay = wx.getStorageSync('moneyPay')
    const GongzhonhaoQRUrl = wx.getStorageSync('GongzhonhaoQRUrl')
    const GongzhonhaoCodePayMark = wx.getStorageSync('GongzhonhaoCodePayMark')
    const discount = wx.getStorageSync('discount')
    console.log('bgcode', bgcode);
    console.log('moneyPay', moneyPay);
    wx.getSystemInfo({
      success: function (res) {
        console.log('获取到的设备信息', res)
        console.warn('config配置项信息', P8SDKConfig);
        if (res.platform == "ios") {
          _this.setData({
            p8site: P8SDKConfig.site_ios,
            p8key: P8SDKConfig.key_ios,
            urlCode: 'https://apps.apple.com/cn/app/id1518752627',
            platform: res.platform,
          })
        } else {
          _this.setData({
            p8site: P8SDKConfig.site_android,
            p8key: P8SDKConfig.key_android,
            urlCode: 'https://xzdjb.yyingplay.com/hotupdate/xxsj_apk/xxsj.apk',
            platform: res.platform,
          })
        }
      }
    });
    this.setData({
      scene: scene,
      webViewFlag: webViewFlag,
      payMark: payMark,
      bgcode: bgcode,
      moneyPay: moneyPay,
      GongzhonhaoQRUrl: GongzhonhaoQRUrl,
      GongzhonhaoCodePayMark:true,
      discount:Number(discount)
    })
    console.warn('支付isShow', _this.data.webViewFlag, '1为显示，2为不显示');
    console.error('onLoad', _this.data)
    if (options.scene) {
      wx.request({
        url: _this.data.sceneUrl + scene,
        method: "GET",
        success: function (res) {
          console.error('scene接口', res)
          if (res.data.result == 0) {
            _this.setData({
              gotoPay_goods: res.data.data.gotoPay_goods,
              gotoPay_character: res.data.data.gotoPay_character,
              gotoPay_area: res.data.data.gotoPay_area,
              gotoPay_order: res.data.data.gotoPay_order,
              gotoPay_price: res.data.data.gotoPay_price,
              gotoPay_site: res.data.data.gotoPay_site,
            })
          }
          console.error('二维码请求scene返回的参数', _this.data, res)
        }
      })

    } else if (options.gotoPay_type === 'h5') {
      // 小程序内嵌 微信支付
      console.log('接收订单参数', options)
      _this.setData({
        gotoPay_goods: options.gotoPay_goods,
        gotoPay_character: options.gotoPay_character,
        gotoPay_area: options.gotoPay_area,
        gotoPay_order: options.gotoPay_order,
        gotoPay_price: options.gotoPay_price,
        gotoPay_site: options.gotoPay_site,
      })
    }



  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.warn('onReady初次渲染完成')
    let _this = this
    let obj = wx.getLaunchOptionsSync()
    let data = _this.data
    let openData = {}
    const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
    if (wxLoginToReportResult) {
      openData = JSON.parse(wxLoginToReportResult)
    }
    _this.setData({
      openid: openData.openid
    })
    let phoneType = wx.getStorageSync('phoneType')
    console.log(phoneType);
    if (openData.status == 0) {
      _this.setData({
        phoneAutoBtn: false,
      })
    } else if (phoneType == '2107') {
      _this.setData({
        phoneAutoBtn: false,
      })
    } else if (_this.data.webViewFlag == '2') {
      _this.setData({
        phoneAutoBtn: false,
      })
    } else {
      _this.setData({
        phoneAutoBtn: true,
      })
    }
    console.error('请求支付接口', _this.data)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(event) {
    let _this = this
    console.warn('onShow页面显示', _this.data)
    let obj = wx.getEnterOptionsSync()
    let data = ''
    if (obj.referrerInfo.appId && obj.referrerInfo.extraData) {
      data = JSON.parse(JSON.stringify(obj.referrerInfo.extraData))
      if (!(_this.data.gotoPay_goods)) {
        this.setData({
          gotoPay_goods: data.gotoPay_goods,
          gotoPay_price: data.gotoPay_price,
          gotoPay_order: data.gotoPay_order,
          gotoPay_character: data.gotoPay_character,
          gotoPay_area: data.gotoPay_area,
          gotoPay_site: data.gotoPay_site
        })
      }
    } else {
      if (_this.data.gotoPay_payStatus == 0 && !_this.data.scene) {
        wx.showToast({
          title: '获取订单参数失败！请重新下单',
          icon: 'none',
          duration: 2000,
          mask: true
        })
      } else {

      }
    }
    console.error('onShow', _this.data)
    if (_this.data.paySkipNum > 0) {
      wx.showLoading({
        title: '检查支付结果',
      })
      let time = parseInt(new Date().getTime() / 1e3);
      let payResultSign = utilMd5.hexMD5(`${_this.data.p8key}WX${_this.data.p8site}WX${time}${time}`);
      wx.request({
        url: _this.data.basePayUrl + '/api/queryPayResult',
        data: {
          order_id: _this.data.gotoPay_order,
          json: 1,
          site: _this.data.p8site,
          sign: payResultSign,
          time: time,
        },
        method: 'GET',
        success: function (res) {
          console.error('支付结果', res.data)
          if (res.data.result === 0) {
            if (res.data.data.is_pay) {
              wx.showToast({
                title: '已支付',
                icon: 'none',
                duration: 1500,
              })
              _this.setData({
                gotoPay_payStatus: 1,
                GongzhonhaoCodePayMark: true,
              })
              wx.setStorageSync('GongzhonhaoCodePayMark', true)
              console.error('支付成功查询用户状态', _this.data.userStatus);
              if (_this.data.userStatus == 3) {
                _this.setData({
                  phoneTips: true,
                })
              }
              if (Number(_this.data.gotoPay_price) > _this.data.moneyPay) {
                _this.setData({
                  payMark: true
                })
                wx.setStorageSync('payMark', true)
              }
            } else {
              wx.showToast({
                title: '未支付',
                icon: 'none',
                duration: 3000,
              })
              _this.setData({
                gotoPay_payStatus: 0,
              })
            }
          } else {
            wx.showToast({
              title: res.data.data.msg,
              icon: 'none'
            })
          }
        }
      })
    }
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.warn('onHide页面隐藏')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  backToGame() {
    let that = this
    that.setData({
      gotoPay_payStatus: 0,
      newTips: false,
      phoneTips: false,
      paySkipNum: 0,
    })
    wx.navigateBack({
      delta: 0,
    })
  },
  closeNewTips() {
    let that = this
    that.setData({
      gotoPay_payStatus: 0,
      newTips: false,
      phoneTips: false,
      paySkipNum: 0,
    })
  },
  payBtn() {
    let _this = this
    console.error('点击开始支付按钮', _this.data);
    let openData = {}
    const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
    if (wxLoginToReportResult) {
      openData = JSON.parse(wxLoginToReportResult)
    }
    let phoneType = wx.getStorageSync('phoneType')
    if (phoneType == '2107') {
      console.error('2107标识，此标识说明该手机号绑定过,如果在测试杀掉小程序再测试试试', phoneType);
    }
    console.error('查询用户状态', JSON.parse(wxLoginToReportResult));
    if (openData.status == 0) {
      _this.skipPay()
      _this.setData({
        userStatus: 0,
      })
    } else if (phoneType == '2107') {
      _this.skipPay()
      _this.setData({
        userStatus: 0,
      })
    } else {
      _this.skipPay()
      _this.setData({
        userStatus: 3,
      })
    }

  },
  cancelBindPhone() {
    let that = this
    console.error('取消绑定按钮', that.data);
    // that.setData({
    //   phoneTips: false,
    // })
    that.backToGame()
  },
  getUrlCode() {
    let that = this
    wx.setClipboardData({
      data: '387270173',
      success(res) {

      }
    })
  },
  getPhoneNumber(e) {
    var that = this;
    var msg = e.detail.errMsg;
    console.error('点击拉取手机号返回', msg);
    var encryptedData = e.detail.encryptedData;
    var iv = e.detail.iv;
    var code = e.detail.code;
    if (msg == 'getPhoneNumber:ok') { //这里表示获取授权成功
      that.getPhoneRequest(code)
    } else {

    }
  },
  getPhoneRequest(code) {
    console.error('getPhoneRequest', code);
    let that = this
    wx.showLoading({
      title: '绑定中...',
      mask: true
    })
    console.error('that.data', that.data);
    let time = parseInt((new Date).getTime() / 1e3);
    let sign = `${that.data.p8site}appid=${appid}&js_code=${code}&site=${that.data.p8site}&time=${time}${that.data.p8key}`
    wx.request({
      url: that.data.baseUrl + '/oauth/getPhoneNumber',
      data: {
        site: that.data.p8site,
        appid: appid,
        js_code: code,
        time: time,
        sign: utilMd5.hexMD5(sign)
      },
      method: 'GET',
      success: function (res) {
        console.log('调用微信手机号', res);
        that.setData({
          'respone.res4': res.data,
        })
        if (res.data.result === 0) {
          let mobile = res.data.data.phoneNumber
          that.setData({
            mobilePhone: res.data.data.phoneNumber
          })
          let webParams = {
            site: that.data.p8site,
            mobile: mobile,
            key: that.data.p8key
          }
          that.gotoWXbindWebView(webParams)
        } else {
          wx.showToast({
            title: res.data.data.msg,
            icon: 'none'
          })
        }
      }
    })
  },
  gotoWXbindWebView(params) {
    let that = this
    console.error('gotoWXbindWebView', params);
    console.error('that.data', that.data);
    const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
    let openData = JSON.parse(wxLoginToReportResult)
    console.error('openData', openData);
    if (that.data.webViewFlag == '1') {
      wx.login({
        success(res) {
          if (res.code) {
            //发起网络请求
            wx.request({
              url: that.data.baseUrl + '/oauth/jscode2Session_two',
              data: {
                js_code: res.code,
                site: params.site,
                appid: appid,
              },
              method: "POST",
              success: function (res) {
                console.error('/oauth/jscode2Session_two', res)
                if (res.data.result == 0) {
                  let openIdData = JSON.parse(res.data.data)
                  that.setData({
                    openid: openIdData.openid
                  })
                  let time = parseInt((new Date).getTime() / 1e3);
                  let sign = `${params.site}mobile=${params.mobile}&openid=${that.data.openid}&site=${params.site}&time=${time}&uid=${openData.uid}${params.key}`
                  console.log(sign);
                  wx.request({
                    url: that.data.baseUrl + '/api/wxBindMobile',
                    data: {
                      site: params.site,
                      mobile: params.mobile,
                      uid: openData.uid,
                      time: time,
                      openid: that.data.openid,
                      sign: utilMd5.hexMD5(sign)
                    },
                    method: 'POST',
                    success: function (res) {
                      console.error('调用微信用户绑定手机成功', res);
                      wx.hideLoading()
                      if (res.data.result === 0) {
                        wx.showToast({
                          title: '绑定手机号成功',
                          icon: 'none',
                        })
                        that.bindPhoneAfter()
                      } else if (res.data.result == 1 && res.data.data.errorcode == '2107') {
                        wx.setStorageSync('phoneType', res.data.data.errorcode)
                        wx.showToast({
                          title: '绑定成功',
                          icon: 'none',
                        })
                        that.bindPhoneAfter()
                      } else {
                        wx.showToast({
                          title: res.data.data.msg,
                          icon: 'none',
                        })
                      }
                      console.error('绑定手机号日志log', res.data);
                      setTimeout(() => {
                        that.updateUserInfo()
                      }, 2000)
                    }
                  })
                }
              }
            })
          }
        }
      })
    }
  },
  bindPhoneAfter() {
    let that = this
    setTimeout(() => {
      that.setData({
        phoneTips: false,
      })
      that.backToGame()
    }, 1000)
  },
  skipPay() {
    let _this = this
    console.error('跳转前查询平台', _this.data.platform);
    let o = wx.getStorageSync('extAppid');
    let r = "pages/pay/pay";
    let s = {
      gotoPay_order: _this.data.gotoPay_order,
      gotoPay_character: _this.data.gotoPay_character,
      gotoPay_area: _this.data.gotoPay_area,
      gotoPay_goods: _this.data.gotoPay_goods,
      gotoPay_price: _this.data.gotoPay_price,
      gotoPay_site: _this.data.gotoPay_site,
    };
    if (_this.data.webViewFlag == '1') {
      wx.openEmbeddedMiniProgram({
        appId: o,
        path: r,
        extraData: s,
        envVersion: "release",
        /* develop trial  release*/
        success(e) {
          _this.setData({
            paySkipNum: ++_this.data.paySkipNum,
            phoneTipsToPay: false,
          })
        },
        fail(e) {

        },
      });
    }

  },
  updateUserInfo() {
    console.error('updateUserinfo');
    let that = this
    let mobile = that.data.mobilePhone
    let time = parseInt(new Date().getTime() / 1e3);
    let a = wx.getStorageSync('wxLoginToReportConfig')
    let params = JSON.parse(a)
    params.mobile = mobile
    params.time = time
    params.sign = utilMd5.hexMD5(`${that.data.p8key}WX${that.data.p8site}WX${time}${time}`);
    wx.login({
      success(loginRes) {
        if (loginRes.code) {
          params.js_code = loginRes.code
          wx.request({
            url: that.data.baseUrl + '/oauth/wxLoginToReport',
            data: params,
            method: "POST",
            success: function (res) {
              if (res.data.result === 0) {
                let e = res.data.data
                console.error('update用户', e);
                wx.setStorageSync('wxLoginToReportResult', JSON.stringify(e))
              } else {
                wx.showToast({
                  title: res.data.data.msg,
                  icon: 'none'
                })
              }
            }
          })
        }
      }
    })
  },
  giftShow() {
    console.log('giftShow');
    this.setData({
      vanOverlay: true,
    })
  },
  closeVanOverlay() {
    this.setData({
      vanOverlay: false,
    })
  },
  handleCatchtouchMove() {
    return
  }
})