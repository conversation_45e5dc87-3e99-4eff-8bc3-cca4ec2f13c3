// app.js
let P8SDKConfig = require("./util/p8sdk-wechat-config");
let utilMd5 = require('./util/md5.js');
let h5_link = ''
let baseUrl = "https://center.yyingplay.com"
let site = ''
let key = ''
let h5_link_o = ""
// let baseH5Url = `${baseUrl}/html/tyh5cbl/index.html#/`
// let baseH5Url = `http://tcenter.play800.cn/html_/tyh5cbl`
let baseH5Url = `${baseUrl}/html_/tyh5cbl`
// https://doom-cdn.dawx.com/Mobile/pl800.html
App({
  globalData: {
    webViewPath: '',
    webViewOriginPath: '',
    webViewAdPath: "",
    scene: '',
    sideBarIndex: 0,
    userInfo: null,
    version: '1.0.0',
    updateTime: '2025年1月'
  },
  onLaunch() {
    let that = this
    wx.getSystemInfo({
      success: function (res) {
        console.log('获取到的设备信息', res)
        console.warn('config配置项信息', P8SDKConfig);
        wx.setStorageSync('platform', res.platform)
        if (res.platform == "ios") {
          site = P8SDKConfig.site_ios
          key = P8SDKConfig.key_ios
          h5_link_o = "https://test-update.locojoy.com/mt2_small/p8/Webgl/index.html"
        } else {
          site = P8SDKConfig.site_android
          key = P8SDKConfig.key_android
          h5_link_o = "https://test-update.locojoy.com/mt2_small/p8/Webgl/index.html"
        }
      }
    });
    that.autoUpdate()
    that.getNavBarColor()
    console.log('app_onLaunch启动', that);
    console.log('h5_link', h5_link);
    console.log('h5_link_o', h5_link_o);
  },
  onShow(options) {
    let that = this
    console.log('onShow', options)
    that.globalData.scene = options.scene;
    wx.removeStorageSync('adResult')
    if (options.scene == 1038 && options.query.adUitld && options.query.gotoPay_type == 'h5' && options.referrerInfo.extraData) {
      let data = JSON.parse(JSON.stringify(options.referrerInfo.extraData))
      wx.setStorageSync('adResult', data)
    }
  },
  onHide() {
    let that = this
    wx.removeStorageSync('adResult')
    that.globalData.webViewPath = h5_link ? h5_link : h5_link_o
    that.globalData.webViewOriginPath = h5_link ? h5_link : h5_link_o
    let P8SDK = require('./util/p8sdk-wechat-mini-ad')
    const wxLoginToReportResult = wx.getStorageSync('wxLoginToReportResult')
    const wxLoginToReportConfig = wx.getStorageSync('wxLoginToReportConfig')
    let appid = wx.getStorageSync('appid')
    let openData = JSON.parse(wxLoginToReportResult)
    let siteData = JSON.parse(wxLoginToReportConfig)
    P8SDK.wxShipInit(openData.openid, '', siteData.site, appid, 'all')
    console.log('app_onHide隐藏', that);
    console.log('h5_link', h5_link);
  },
  getNavBarColor() {
    let that = this
    let i = `${baseUrl}/switch/switchList`;
    // let i = `https://tcenter.play800.cn/switch/switchList`;
    let t = parseInt((new Date).getTime() / 1e3);
    let d = {
      site: site,
      type: '56',
      default: '0',
      time: t,
      ext: "nav_bg_color|h5_link",
    }
    let a = that.SignGetForCenter(d, key)
    d.sign = utilMd5.hexMD5(a);
    let n = (t) => {
      // console.error("rrr1-------------", t);
      t = that.dateOrRes(t);
      console.error("56", JSON.stringify(t));
      if (t.result === 0) {
        wx.setStorageSync('navBgColor', t.data.nav_bg_color)
        wx.setStorageSync('h5_link', t.data.h5_link)
        h5_link = t.data.h5_link
        that.globalData.webViewPath = t.data.h5_link ? h5_link_o : h5_link_o
        that.globalData.webViewOriginPath = t.data.h5_link ? h5_link_o : h5_link_o
      } else {
        console.error("加载navBgColor异常", JSON.stringify(t));
      }
    };
    that.wxRequest(i, "GET", d, n);
  },
  wxRequest(e, t, a, i, n) {
    wx.request({
      url: e,
      method: t,
      data: a,
      success: (e) => {
        if (i) {
          i(e);
        }
      },
      fail: (e) => {
        if (n) {
          n(e);
        }
      },
    });
  },
  SignGetForCenter(e, key) {
    let that = this
    let t = key;
    that.print("新的加密方式请求数据sdkData.key", t);
    var a = [];
    for (var i in e) {
      a.push(i);
    }
    a = a.sort();
    var n = "";
    for (var o = 0; o < a.length; o++) {
      var r = e[a[o]];
      if (o != a.length - 1) {
        n += a[o] + "=" + r + "&";
      } else {
        n += a[o] + "=" + r;
      }
    }
    return e.site + n + t;
  },
  dateOrRes(e) {
    return e.data ? e.data : e;
  },
  print() {
    let t = "";
    for (let e = 0; e < 20; e++) {
      if (!arguments[e]) {
        console.info(t);
        return;
      }
      t += " " + JSON.stringify(arguments[e]);
    }
  },
  autoUpdate: function () {
    var self = this // 获取小程序更新机制兼容 
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager() //1. 检查小程序是否有新版本发布  
      console.log('updateManager', updateManager);
      updateManager.onCheckForUpdate(function (res) { // 请求完新版本信息的回调 
        console.log('onCheckForUpdate', res);
        if (res.hasUpdate) { //2. 小程序有新版本，则静默下载新版本，做好更新准备                                                 
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  //3. 新的版本已经下载好，调用applyUpdate应用新版本并重启  
                  updateManager.applyUpdate()
                } else if (res.cancel) {
                  //不应用 
                }
              }
            })
          })

          updateManager.onUpdateFailed(function () {
            // 新的版本下载失败
            wx.showModal({
              title: '已经有新版本了哟~',
              content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
            })
          })
        }
      })
    } else {
      // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示     
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
      })
    }
  },
})