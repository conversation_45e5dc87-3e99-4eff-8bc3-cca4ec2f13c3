/* component/privacy/privacy.wxss */
.privacy {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, .5);
    z-index: 9999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content {
    width: 632rpx;
    padding: 48rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx;
}

.content .title {
    text-align: center;
    color: #333;
    font-weight: bold;
    font-size: 32rpx;
}

.content .des {
    font-size: 26rpx;
    color: #666;
    margin-top: 40rpx;
    text-align: justify;
    line-height: 1.6;
}

.content .des .link {
    color: #07c160;
    text-decoration: underline;
}

.btns {
    margin-top: 48rpx;
    display: flex;
}

.btns .item {
    justify-content: space-between;
    width: 244rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16rpx;
    box-sizing: border-box;
    border: none;
}

.btns .reject {
    background: #f4f4f5;
    color: #909399;
}

.btns .agree {
    background: #07c160;
    color: #fff;
}