<!--pages/pay/pay.wxml-->
<movable-area class="movable-area">
  <view class="container" wx:if="{{webViewFlag === 1}}">
    <view class="payStaus">支付状态：{{gotoPay_payStatus === 1 ?'已支付':'未支付'}}</view>
    <button type="default" class="sumbit-btn" bindtap="payBtn">开始支付</button>
    <button type="primary" class="backToGame-btn" bindtap="backToGame">返回首页</button>
    <view class="gameDetail">
      <view class="gameContent">
        <view class="gameMessage">商品：{{gotoPay_goods}}</view>
        <view class="gameMessage">金额：<text>{{gotoPay_price}}元</text></view>
        <!-- <view class="gameMessage">应付金额：<text class="nicePrice">{{gotoPay_price * discount / 100}}</text></view> -->
        <view class="gameMessage">订单：<text class="fsNormal">{{gotoPay_order}}</text></view>
        <view class="gameMessage">角色：{{gotoPay_character}}</view>
        <view class="gameMessage">区服：{{gotoPay_area}}</view>
      </view>
    </view>
    <view wx:if="{{GongzhonhaoCodePayMark}}">
      <view class="footer flex_center">
        <image src="https://dsp.play800.cn/storage/work/202312/cca411c6df306010f78627c16867e6b0.png" mode="aspectFit" style="width: 667rpx;height: 375rpx;" />
        <!-- <image src="../../assets/image/starsGongzhonghao.png" mode="aspectFit" style="width: 667rpx;height: 375rpx;" /> -->
      </view>
      <view>
        <view class="flex_center">
          <image src="{{GongzhonhaoQRUrl}}" mode="aspectFit" show-menu-by-longpress="{{true}}" style="width: 344rpx;height: 344rpx;" />
        </view>
        <view style="color: #000;font-size: 32rpx;font-family:cursive;text-align: center;font-weight: 600;">长 按 关 注 公 众 号</view>
      </view>
    </view>
  </view>
  <movable-view class="movable-view" direction="all" x="6000rpx" y="900rpx">
    <!-- <view class="stableVersionBtn" hover-class='highStableVersionBtn' bindtap="stableVersionBtn">
      <text>稳定版本</text>
      <view class="stableVersionBarge">荐</view>
    </view> -->
    <view wx:if="{{payMark}}">
      <image src="https://dsp.play800.cn/storage/work/202307/4192c9acb1caf5ac2f8124aa60c51fd6.png" style="width: 92rpx;height: 43rpx;position: absolute;z-index: 1;bottom: -35rpx;left: 20rpx;" class="giftLogo" bindtap="giftShow"></image>
      <image src="https://dsp.play800.cn/storage/work/202307/adc1b4debd11449cf70581eb002d5c7e.png" style="width: 135rpx;height: 167rpx;position: relative;" bindtap="giftShow" class="giftLogo"></image>
    </view>
  </movable-view>
</movable-area>

<view wx:if="{{false}}">
  <view wx:if="{{newTips}}">
    <view class="mask"></view>
    <view class="pay_box">
      <image src="../../assets/image/pay_bg.png" style="width: 100%;height: 100%;position: absolute;z-index: -999;"></image>
      <image src="../../assets/image/x.png" style="width: 48rpx;height: 53rpx;position: absolute;right: 0;top: 0;" bindtap="closeNewTips"></image>
      <view style="display: flex;justify-content: center;padding: 40rpx 40rpx 40rpx;">
        <!-- <image src="../../assets/image/pay.png" style="width: 339rpx;height: 88rpx;" /> -->
      </view>
      <view style="width: 100%;height: 296rpx;display: flex;justify-content: center;">
        <view style="width: 611rpx;">
          <image src="../../assets/image/pay_ca.png" style="width: 611rpx;height: 296rpx;position: absolute;z-index: -999;"></image>
          <view style="padding: 40rpx 40rpx 30rpx 40rpx;font-size: 36rpx;font-weight: 600;text-align: center;">IOS用户支付请加Q群</view>
          <view style="padding: 12rpx 20rpx;font-size: 24rpx;font-weight: 600;text-align: center;"></view>
          <view style="padding:40rpx 20rpx;font-size: 24rpx;font-weight: 600;text-align: center;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;display: flex;align-items: center;justify-content: center;">
            <text style="display: flex;align-items: center;justify-content: flex-end;flex: 1;margin: 0 40rpx;">群号: 387270173</text>
            <view style="display: flex;justify-content: flex-end;">
              <image src="../../assets/image/copy.png" style="width: 160rpx;height: 52rpx;" bindtap="getUrlCode" />
            </view>
          </view>
        </view>
      </view>
      <view style="display: flex;justify-content: center;padding: 20rpx 20rpx 0 20rpx">
        <!-- <image src="../../assets/image/copy.png" style="width: 160rpx;height: 56rpx;position: absolute;right: 60rpx;top: 260rpx;" bindtap="getUrlCode" /> -->
        <!-- <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class="phoneBtn" style="width: 182rpx;height: 67rpx;position: absolute;background-color: transparent;" wx:if="{{phoneAutoBtn}}"></button> -->
      </view>
      <view style="display: flex;justify-content: center;padding: 5rpx 20rpx 0 20rpx">
        <!-- <image src="../../assets/image/tips.png" style="width: 360rpx;height: 40rpx;" /> -->
      </view>
      <view style="padding: 8rpx 20rpx;font-size: 28rpx;font-weight: 600;text-align: center;">群里还有很多礼包码拿噢!</view>
    </view>
  </view>
</view>

<view wx:if="{{false}}">
  <view wx:if="{{phoneTips}}">
    <view class="mask"></view>
    <view class="pay_box">
      <image src="../../assets/image/pay_bg.png" style="width: 100%;height: 100%;position: absolute;z-index: -999;"></image>
      <view style="display: flex;justify-content: center;padding: 40rpx 40rpx 40rpx;">
        <!-- <image src="../../assets/image/pay.png" style="width: 339rpx;height: 88rpx;" /> -->
      </view>
      <view style="width: 100%;height: 296rpx;display: flex;justify-content: center;">
        <view style="width: 611rpx;">
          <view style="padding: 40rpx 0;font-size: 36rpx;font-weight: 600;text-align: center;">尊敬的用户，为了您的账号安全和给您提供更好的服务，支付前请先绑定手机号</view>
        </view>
      </view>
      <view style="display: flex;justify-content: space-around;padding: 20rpx 20rpx 0 20rpx">
        <image src="../../assets/image/ZY_03.png" style="width: 182rpx;height: 67rpx;" />
        <view style="width: 182rpx;height: 67rpx;color: #fff;font-weight: 600;text-align: center;line-height: 67rpx;position: absolute;left: 13%;" bindtap="cancelBindPhone">取消</view>
        <view style="width: 182rpx;height: 67rpx;color: #fff;font-weight: 600;text-align: center;line-height: 67rpx;position: absolute;right: 13%;">绑定</view>
        <image src="../../assets/image/ZY_03.png" style="width: 182rpx;height: 67rpx;" />
        <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class="phoneBtn" style="width: 240rpx;height: 67rpx;position: absolute;background-color: transparent;right: 9%;padding: 0;" wx:if="{{phoneAutoBtn}}"></button>
      </view>
    </view>
  </view>
</view>

<view wx:if="{{false}}">
  <view wx:if="{{phoneTipsToPay}}">
    <view class="mask"></view>
    <view class="pay_box" style="height: 419rpx;">
      <image src="../../assets/image/COM_22.png" style="width: 100%;height: 100%;position: absolute;z-index: -999;"></image>
      <view style="display: flex;justify-content: center;padding: 20rpx 20rpx 0 20rpx;height: calc(100% - 40rpx);align-items: center;">
        <image src="../../assets/image/pay_btn.png" style="width: 404rpx;height: 117rpx;" bindtap="skipPay" />
      </view>
    </view>
  </view>
</view>

<van-overlay show="{{ vanOverlay }}" z-index="101" wx:if="{{false}}">
  <view style="height: 100%;left: 0;position: fixed;top: 0;width: 100%;z-index: 102;" bindtap="closeVanOverlay"></view>
  <view class="wrapper" style="position: fixed;left: 50%;top: 50%;transform: translate(-50%,-50%);z-index: 103;">
    <image src="https://dsp.play800.cn/storage/work/202307/11cfc1992e01ea024aa00621f470f058.png" style="width: 648rpx;height: 700rpx;"></image>
    <view style="width: 640rpx;height: 610rpx;position: absolute;left: 5rpx;top: 60rpx;">
      <!-- <image src="{{bgcode}}" show-menu-by-longpress="{{true}}" style="width: 100%;height: 100%;" /> -->
      <image src="{{bgcode}}" show-menu-by-longpress="{{true}}" style="width: 100%;height: 100%;" />
    </view>
    <view style="display: flex;justify-content: center;align-items: center;">
      <image src="../../assets/image/blueBar.png" style="width:514rpx;height:60rpx"></image>
      <view style="position: absolute;color: #fff;font-size: 32rpx;font-family:cursive;">长 按 图 片 识 别 二 维 码</view>
    </view>
  </view>
</van-overlay>

<view wx:if="{{webViewFlag == 1}}">
  <view class="privacy" wx:if="{{phoneTips}}" catchtouchmove="handleCatchtouchMove">
    <view class="content">
      <view class="title">绑定手机号</view>
      <view class="des">尊敬的用户，为了您的账号安全和给您提供更好的服务，请绑定手机号</view>
      <view class="btns">
        <button class="item reject" bind:tap="cancelBindPhone">取消</button>
        <button id="agree-btn" class="item agree" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">绑定</button>
      </view>
    </view>
  </view>
</view>


<!-- <Privacy bind:getPhone="getPhoneNumber" /> -->